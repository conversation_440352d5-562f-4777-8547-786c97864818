.controls {
  background-color: $background;
  padding: 12px;
  border-radius: 12px;
}

.screenshot-control,
.wifi-control,
.bluetooth-control,
.idle-control {
  background-color: mix($background, $color1, 90%);
  padding: 10px;
  border-radius: 8px;
  transition: background-color 0.3s ease, transform 0.2s ease;
  font-size: 20px;
  min-width: 18px;
}

.volume-control,
.brightness-control,
.microphone-control { 
  background-color: mix($background, $color1, 90%);
  padding: 4px;
  border-radius: 8px;
  transition: background-color 0.3s ease, transform 0.2s ease;
  font-size: 16px;
  min-width: 16px;
}

.screenshot-control:hover,
.wifi-control:hover,
.bluetooth-control:hover,
.idle-control:hover {
  background-color: mix($background, $color1, 80%);
}

.screenshot-icon-control,
.wifi-icon-control,
.idle-icon-control {
  margin: 0 10px 0 4px;
}

.volume-icon-control,
.brightness-icon-control{
  margin: 0 10px 0 6px;
}

.bluetooth-icon-control {
  margin: 0;
}

.brightness-control-bar trough,
.volume-control-bar trough {
  background-color: mix($background, $foreground, 80%);
  border-radius: 5px;
  min-height: 8px;
  min-width: 170px;
  margin: 6px 0;
}

.brightness-control-bar trough highlight,
.volume-control-bar trough highlight {
  min-height: 8px;
  background-color: mix($color5, $foreground, 90%);
  border-radius: 5px;
}

