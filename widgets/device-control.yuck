;; Wifi ;;
(defwidget wifi-control []
  (eventbox :cursor "pointer"
  (box  :class "wifi-control"
        :orientation "vertical"
        :space-evenly "false"
    (button :class "wifi-icon-control"
            :onclick "~/.config/eww/scripts/wifi-menu.sh" wifi-strength))))

;; Volume ;;
(defwidget volume-control []
  (eventbox :cursor "pointer"
  (box  :class "volume-control"
        :orientation "horizontal"
        :space-evenly "false"
    (button :class "volume-icon-control"
            :onclick "~/.config/eww/scripts/pavucontrol.sh" volume-icon))))

;; Volume Bar ;;
(defwidget volume-control-bar []
  (eventbox :cursor "pointer"
        (scale :class "volume-control-bar"
               :value current-volume
               :orientation "horizontal"
               :max 101
               :min 0
               :onchange "pactl set-sink-volume $(pactl get-default-sink) {}%")))


;; Brightness ;;
(defwidget brightness-control []
  (eventbox :cursor "pointer"
  (box  :class "brightness-control"
        :orientation "horizontal"
        :space-evenly "false"
    (button :class "brightness-icon-control"
            :onclick "" "󰖨"))))

;; Brightness Bar ;;
(defwidget brightness-control-bar []
  (eventbox :cursor "pointer"
        (scale :class "brightness-control-bar"
               :value current-brightness
               :orientation "horizontal"
               :max 101
               :min 0
               :onchange "brightnessctl s {}%")))

;; Bluetooth ;;
(defwidget bluetooth-control []
  (eventbox :cursor "pointer"
  (box  :class "bluetooth-control"
        :orientation "vertical"
        :space-evenly "false"
    (button :class "bluetooth-icon-control"
            :onclick "~/.config/eww/scripts/bluetuith.sh" bluetooth-status))))

;; Idle ;;
(defwidget idle-control []
  (eventbox :cursor "pointer"
  (box  :class "idle-control"
        :orientation "vertical"
        :space-evenly "false"
    (button :class "idle-icon-control"
            :onclick "~/.config/eww/scripts/toggle-idle.sh" {idle ? "" : ""  }))))

;; Screenshot ;;
(defwidget screenshot-control []
  (eventbox :cursor "pointer"
  (box  :class "screenshot-control"
        :orientation "vertical"
        :space-evenly "false"
    (button :class "screenshot-icon-control"
            :onclick "sh ~/scripts/screenshot.sh &" ""))))

;; Volume Group ;;
(defwidget volume-controls []
  (box  :class "volume-controls"
        :orientation "horizontal"
        :space-evenly "false"
        :spacing 10
        :halign "start"
        :vexpand "false"
        :hexpand "false"
        (volume-control)
        (volume-control-bar)))

;; Brightness Group ;;
(defwidget brightness-controls []
  (box  :class "brightness-controls"
        :orientation "horizontal"
        :space-evenly "false"
        :spacing 10
        :halign "start"
        :vexpand "false"
        :hexpand "false"
        (brightness-control)
        (brightness-control-bar)))

;; Grid Control Group ;;
(defwidget toprow-controls []
  (box  :class "toprow-controls"
        :orientation "horizontal"
        :space-evenly "true"
        :spacing 10
        :halign "start"
        :vexpand "false"
        :hexpand "false"
        (wifi-control)
        (bluetooth-control)
        (idle-control)
        (screenshot-control)))

;; Controls Group ;;
(defwidget controls []
  (box :class "controls"
       :orientation "vertical"
       :space-evenly "false"
       :spacing 8
       :halign "start"
       :vexpand "false"
       :hexpand "false"
       (bar-controls)
       (toprow-controls)))

;; Bar Group ;;
(defwidget bar-controls []
  (box :class "bar-controls"
       :orientation "vertical"
       :space-evenly "true"
       :spacing 8
       :halign "start"
       :vexpand "false"
       :hexpand "false"
       (volume-controls)
       (brightness-controls)))

;; device-control ;;
(defwindow device-control
  :monitor '[1, 0]'
  :geometry (geometry :x "0.8%"
                      :y "16px"
                      :width "10%"
                      :height "0%"
                      :anchor "top right")
  :stacking "bg"
  :exclusive "false"
  :focusable "false"
  (controls))



